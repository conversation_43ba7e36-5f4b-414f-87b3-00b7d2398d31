{"id": "24b2ce68-e9a8-49ff-9181-ac878bbfe9ec", "revision": 0, "last_node_id": 75, "last_link_id": 95, "nodes": [{"id": 52, "type": "VAELoader", "pos": [1576.9969482421875, 5984.556640625], "size": [360, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"label": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}}], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [64, 79]}], "properties": {"Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 53, "type": "Note", "pos": [1346.996826171875, 5984.556640625], "size": [210, 159.49227905273438], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["This model uses the wan 2.1 VAE.\n\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 55, "type": "ModelSamplingSD3", "pos": [2026.8023681640625, 5551.05810546875], "size": [315, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 58}, {"label": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [68]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002]}, {"id": 56, "type": "LayerUtility: PurgeVRAM", "pos": [3127.80126953125, 6055.623046875], "size": [315, 82], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 59}, {"label": "purge_cache", "name": "purge_cache", "type": "BOOLEAN", "widget": {"name": "purge_cache"}}, {"label": "purge_models", "name": "purge_models", "type": "BOOLEAN", "widget": {"name": "purge_models"}}], "outputs": [], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 57, "type": "Note", "pos": [916.9197998046875, 5511.5654296875], "size": [210, 159.49227905273438], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["This model uses a different diffusion model for the first steps (high noise) vs the last steps (low noise).\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 58, "type": "UNETLoader", "pos": [809.1534423828125, 5326.12158203125], "size": [430, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}}, {"label": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [76]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_i2v_high_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 59, "type": "PathchSageAttentionKJ", "pos": [1688.7637939453125, 5666.8154296875], "size": [315, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 60}, {"label": "sage_attention", "name": "sage_attention", "type": "COMBO", "widget": {"name": "sage_attention"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [61]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 61, "type": "ModelSamplingSD3", "pos": [2044.3323974609375, 5674.57421875], "size": [315, 58], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 61}, {"label": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [72]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8]}, {"id": 63, "type": "VAEDecode", "pos": [3182.70849609375, 5627.2451171875], "size": [210, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 63}, {"label": "vae", "name": "vae", "type": "VAE", "link": 64}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [62]}], "properties": {"Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 64, "type": "JWInteger", "pos": [1698.0419921875, 6808.56591796875], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "INT", "widget": {"name": "value"}}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [81]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [704]}, {"id": 65, "type": "JWInteger", "pos": [1705.486572265625, 6974.8994140625], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "INT", "widget": {"name": "value"}}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [82]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [544]}, {"id": 66, "type": "UNETLoader", "pos": [831.9097900390625, 5698.1875], "size": [430, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}}, {"label": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [84]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_i2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 67, "type": "PathchSageAttentionKJ", "pos": [1833.25830078125, 5375.58935546875], "size": [315, 58], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 65}, {"label": "sage_attention", "name": "sage_attention", "type": "COMBO", "widget": {"name": "sage_attention"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [58]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 68, "type": "LayerUtility: PurgeVRAM", "pos": [2734.83056640625, 6039.78759765625], "size": [315, 82], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 66}, {"label": "purge_cache", "name": "purge_cache", "type": "BOOLEAN", "widget": {"name": "purge_cache"}}, {"label": "purge_models", "name": "purge_models", "type": "BOOLEAN", "widget": {"name": "purge_models"}}], "outputs": [], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 69, "type": "JWInteger", "pos": [1706.8199462890625, 7120.455078125], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "value", "name": "value", "type": "INT", "widget": {"name": "value"}}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [83]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [81]}, {"id": 60, "type": "CLIPLoader", "pos": [1524.1048583984375, 5811.32177734375], "size": [360, 106], "flags": {"pinned": true}, "order": 8, "mode": 0, "inputs": [{"label": "clip_name", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}}, {"label": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}}, {"label": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}}], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [57, 67]}], "properties": {"Node name for S&R": "CLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 74, "type": "WanImageToVideo", "pos": [2189.197998046875, 6379.37841796875], "size": [342.5999755859375, 210], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 77}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 78}, {"label": "vae", "name": "vae", "type": "VAE", "link": 79}, {"label": "clip_vision_output", "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT"}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 80}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 81}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 82}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 83}, {"label": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [69, 73]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [70, 74]}, {"label": "latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [71]}], "properties": {"Node name for S&R": "WanImageToVideo", "widget_ue_connectable": {}}, "widgets_values": [704, 544, 81, 1]}, {"id": 54, "type": "LoadImage", "pos": [1372.594970703125, 6206.0751953125], "size": [450, 540], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}}, {"label": "upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [80]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["c5f8f3e3e75e37761bcedc3a9600ced7fd08ea4fe4588daf2c806ba4f33e02fd.png", "image"]}, {"id": 70, "type": "CLIPTextEncode", "pos": [1952.43505859375, 5797.74609375], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 67}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [77]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["A girl crouched down and picked up a coin from the ground"], "color": "#232", "bgcolor": "#353"}, {"id": 51, "type": "CLIPTextEncode", "pos": [1959.9969482421875, 6033.556640625], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 57}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [78]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#322", "bgcolor": "#533"}, {"id": 73, "type": "LoraLoaderModelOnly", "pos": [1344.2286376953125, 5338.90771484375], "size": [404.688720703125, 145.72618103027344], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 76}, {"label": "lora_name", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}}, {"label": "strength_model", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [65]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.2-Lightning_T2V-A14B-4steps-lora_HIGH_fp16.safetensors", 2.0000000000000004]}, {"id": 71, "type": "KSamplerAdvanced", "pos": [2463.64794921875, 5609.49365234375], "size": [304.748046875, 334], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 68}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 69}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 70}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 71}, {"label": "add_noise", "name": "add_noise", "type": "COMBO", "widget": {"name": "add_noise"}}, {"label": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}}, {"label": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}}, {"label": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}}, {"label": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}}, {"label": "start_at_step", "name": "start_at_step", "type": "INT", "widget": {"name": "start_at_step"}}, {"label": "end_at_step", "name": "end_at_step", "type": "INT", "widget": {"name": "end_at_step"}}, {"label": "return_with_leftover_noise", "name": "return_with_leftover_noise", "type": "COMBO", "widget": {"name": "return_with_leftover_noise"}}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [66, 75]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 0, "fixed", 8, 1, "euler", "simple", 0, 4, "enable"]}, {"id": 72, "type": "KSamplerAdvanced", "pos": [2807.412841796875, 5598.97509765625], "size": [304.748046875, 334], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 72}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 73}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 74}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 75}, {"label": "add_noise", "name": "add_noise", "type": "COMBO", "widget": {"name": "add_noise"}}, {"label": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}}, {"label": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}}, {"label": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}}, {"label": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}}, {"label": "start_at_step", "name": "start_at_step", "type": "INT", "widget": {"name": "start_at_step"}}, {"label": "end_at_step", "name": "end_at_step", "type": "INT", "widget": {"name": "end_at_step"}}, {"label": "return_with_leftover_noise", "name": "return_with_leftover_noise", "type": "COMBO", "widget": {"name": "return_with_leftover_noise"}}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [59, 63]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 0, "fixed", 8, 1, "euler", "simple", 4, 10000, "disable"]}, {"id": 75, "type": "LoraLoaderModelOnly", "pos": [1328.265625, 5555.85302734375], "size": [334.0391845703125, 97.57750701904297], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 84}, {"label": "lora_name", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}}, {"label": "strength_model", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [60]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.2-Lightning_T2V-A14B-4steps-lora_LOW_fp16.safetensors", 2.0000000000000004]}, {"id": 62, "type": "VHS_VideoCombine", "pos": [3640.22607421875, 5311.5439453125], "size": [688.0678100585938, 888.2341918945312], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 62}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"label": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}}, {"label": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}}, {"label": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"label": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}}, {"label": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}}, {"label": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}}, {"label": "no_preview", "name": "no_preview", "shape": 7, "type": "BOOLEAN", "widget": {"name": "no_preview"}}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4.json", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "AnimateDiff_00001_ehllm_1754329174.mp4", "workflow": "AnimateDiff_00001.png", "fullpath": "/data/ComfyUI/personal/97e72773b3062cc68e83823cfbb22717/output/AnimateDiff_00001.mp4", "format": "video/h264-mp4.json", "subfolder": "", "type": "output", "frame_rate": 16}}}}], "links": [[57, 60, 0, 51, 0, "CLIP"], [58, 67, 0, 55, 0, "MODEL"], [59, 72, 0, 56, 0, "*"], [60, 75, 0, 59, 0, "MODEL"], [61, 59, 0, 61, 0, "MODEL"], [62, 63, 0, 62, 0, "IMAGE"], [63, 72, 0, 63, 0, "LATENT"], [64, 52, 0, 63, 1, "VAE"], [65, 73, 0, 67, 0, "MODEL"], [66, 71, 0, 68, 0, "*"], [67, 60, 0, 70, 0, "CLIP"], [68, 55, 0, 71, 0, "MODEL"], [69, 74, 0, 71, 1, "CONDITIONING"], [70, 74, 1, 71, 2, "CONDITIONING"], [71, 74, 2, 71, 3, "LATENT"], [72, 61, 0, 72, 0, "MODEL"], [73, 74, 0, 72, 1, "CONDITIONING"], [74, 74, 1, 72, 2, "CONDITIONING"], [75, 71, 0, 72, 3, "LATENT"], [76, 58, 0, 73, 0, "MODEL"], [77, 70, 0, 74, 0, "CONDITIONING"], [78, 51, 0, 74, 1, "CONDITIONING"], [79, 52, 0, 74, 2, "VAE"], [80, 54, 0, 74, 4, "IMAGE"], [81, 64, 0, 74, 5, "INT"], [82, 65, 0, 74, 6, "INT"], [83, 69, 0, 74, 7, "INT"], [84, 66, 0, 75, 0, "MODEL"]], "groups": [{"id": 1, "title": "B站、Youtube：T8star-Aix", "bounding": [763.0107421875, 4530.5263671875, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "ds": {"scale": 1.7088742511245918, "offset": [-806.7696833286203, -5359.668455343165]}, "frontendVersion": "1.23.0"}, "version": 0.4}