{"id": "5d16550c-ee49-407c-bb49-7edacf451b6d", "revision": 0, "last_node_id": 1129, "last_link_id": 2075, "nodes": [{"id": 829, "type": "GetNode", "pos": [2992.550048828125, -3357.626953125], "size": [210, 34], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1890]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 906, "type": "SetNode", "pos": [672.997314453125, -2944.046875], "size": [210, 50], "flags": {"collapsed": true}, "order": 86, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1834}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 854, "type": "SetNode", "pos": [687.536376953125, -3291.4990234375], "size": [210, 50], "flags": {"collapsed": true}, "order": 84, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1628}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 851, "type": "SetNode", "pos": [630.642333984375, -3404.66845703125], "size": [210, 50], "flags": {"collapsed": true}, "order": 75, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1625}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 852, "type": "SetNode", "pos": [625.217041015625, -3512.171630859375], "size": [210, 50], "flags": {"collapsed": true}, "order": 83, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1626}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 868, "type": "GetNode", "pos": [2994.461669921875, -3405.923583984375], "size": [210, 50], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1889]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 996, "type": "GetNode", "pos": [4631.58251953125, -2120.239990234375], "size": [210, 50], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [2069]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["fps"], "color": "#232", "bgcolor": "#353"}, {"id": 1000, "type": "KSamplerAdvanced", "pos": [4555.26513671875, -3470.677978515625], "size": [244.748046875, 334], "flags": {}, "order": 73, "mode": 4, "inputs": [{"label": "模型", "name": "model", "type": "MODEL", "link": null}, {"label": "正面条件", "name": "positive", "type": "CONDITIONING", "link": null}, {"label": "负面条件", "name": "negative", "type": "CONDITIONING", "link": null}, {"label": "Latent", "name": "latent_image", "type": "LATENT", "link": null}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 1844}], "outputs": [{"label": "Latent", "name": "LATENT", "type": "LATENT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {"steps": true, "cfg": true, "sampler_name": true, "scheduler": true}}, "widgets_values": ["disable", 122115521059811, "randomize", 20, 1, "uni_pc", "simple", 4, 888, "disable"]}, {"id": 918, "type": "GetNode", "pos": [2991.546630859375, -3304.51806640625], "size": [210, 50], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1809, 1891]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 943, "type": "GetNode", "pos": [3147.1689453125, -1042.6251220703125], "size": [210, 34], "flags": {"collapsed": true}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1895]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1012, "type": "WanVideoVAELoader", "pos": [979.5056762695312, -2799.463623046875], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [1868]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVAELoader", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 1019, "type": "SetNode", "pos": [1362.302978515625, -2678.047607421875], "size": [210, 60], "flags": {}, "order": 72, "mode": 0, "inputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "link": 1868}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_WANVAE", "properties": {"widget_ue_connectable": {}, "previousName": "vae1111"}, "widgets_values": ["vae1111"]}, {"id": 1020, "type": "GetNode", "pos": [3689.950439453125, -3651.59423828125], "size": [210, 60], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [1869]}], "title": "Get_vae1111", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["vae1111"]}, {"id": 1021, "type": "GetNode", "pos": [4241.26123046875, -3753.2978515625], "size": [210, 34], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [1871]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 916, "type": "GetNode", "pos": [3727.03369140625, -3056.426025390625], "size": [210, 58], "flags": {"collapsed": true}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1844, 1881]}], "title": "Get_steps", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 887, "type": "GetNode", "pos": [3136.924560546875, -984.8661499023438], "size": [210, 50], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1892]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1008, "type": "WanVideoTorchCompileSettings", "pos": [937.1968383789062, -3658.81201171875], "size": [390.5999755859375, 202], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "torch_compile_args", "name": "torch_compile_args", "type": "WANCOMPILEARGS", "slot_index": 0, "links": [1857]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoTorchCompileSettings", "widget_ue_connectable": {}}, "widgets_values": ["inductor", false, "default", false, 64, true, 128]}, {"id": 895, "type": "GetNode", "pos": [3319.255859375, -1158.286376953125], "size": [235.99569702148438, 50], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [2059]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["img_part2_frames_Control"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 947, "type": "INTConstant", "pos": [290.4085388183594, -3430.61865234375], "size": [300.906005859375, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "INT", "links": [1625]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [832], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 888, "type": "GetNode", "pos": [3145.657958984375, -928.5645141601562], "size": [210, 34], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1893]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1031, "type": "GetNode", "pos": [3494.982177734375, -1237.666015625], "size": [210, 58], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [1888]}], "title": "Get_vae1111", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["vae1111"]}, {"id": 848, "type": "GetNode", "pos": [3164.4033203125, -851.2444458007812], "size": [210, 34], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": [2057]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["img_part2_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 1029, "type": "WanVideoSLG", "pos": [4057.013427734375, -1062.853759765625], "size": [315, 106], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"label": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [1883]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoSLG", "widget_ue_connectable": {}}, "widgets_values": ["8", 0.30000000000000004, 0.7000000000000002]}, {"id": 1032, "type": "GetNode", "pos": [4122.38134765625, -1274.35498046875], "size": [210, 34], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [1897]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 1035, "type": "GetNode", "pos": [4641.224609375, -1135.9490966796875], "size": [210, 58], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [1901]}], "title": "Get_vae1111", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["vae1111"]}, {"id": 1015, "type": "CLIPTextEncode", "pos": [2003.340576171875, -2932.752685546875], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 104, "mode": 0, "inputs": [{"label": "CLIP", "name": "clip", "type": "CLIP", "link": 1861}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 1968}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [2047]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["一个女孩向镜头招手，微笑，很开心", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 1060, "type": "PrimitiveNode", "pos": [1260.4058837890625, -2351.986572265625], "size": [210, 82], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "widget": {"name": "index"}, "links": [1961]}], "properties": {"Run widget replace on values": false, "widget_ue_connectable": {}}, "widgets_values": [0, "fixed"]}, {"id": 1064, "type": "Text Load Line From File", "pos": [1966.4586181640625, -2024.559814453125], "size": [270, 174], "flags": {}, "order": 118, "mode": 0, "inputs": [{"label": "多行文本", "name": "multiline_text", "shape": 7, "type": "STRING", "link": 1962}, {"label": "index", "name": "index", "type": "INT", "widget": {"name": "index"}, "link": 1963}], "outputs": [{"label": "文本行", "name": "line_text", "type": "STRING", "links": [1964]}, {"label": "字典", "name": "dictionary", "type": "DICT", "links": null}], "properties": {"cnr_id": "was-ns", "ver": "3.0.0", "Node name for S&R": "Text Load Line From File", "widget_ue_connectable": {}}, "widgets_values": ["", "[filename]", "TextBatch", "index", 0]}, {"id": 1014, "type": "CLIPLoaderGGUF", "pos": [1477.3218994140625, -2830.5029296875], "size": [359.7388916015625, 86.94934844970703], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [1860, 1861, 1965]}], "title": "CLIPLoader （GGUF）", "properties": {"cnr_id": "ComfyUI-GGUF", "ver": "a2b75978fd50c0227a58316619b79d525b88e570", "Node name for S&R": "CLIPLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["umt5-xxl-encoder-Q4_K_S.gguf", "wan"], "color": "#432", "bgcolor": "#653"}, {"id": 1013, "type": "CLIPTextEncode", "pos": [1969.3001708984375, -2645.039794921875], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 77, "mode": 0, "inputs": [{"label": "CLIP", "name": "clip", "type": "CLIP", "link": 1860}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [2049, 2051]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 1065, "type": "CLIPTextEncode", "pos": [1964.1322021484375, -2301.394775390625], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 124, "mode": 0, "inputs": [{"label": "CLIP", "name": "clip", "type": "CLIP", "link": 1965}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 1964}], "outputs": [{"label": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [2053]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["一个女孩向镜头招了招手，向镜头走来，比了一个心的动作", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 1062, "type": "Text Load Line From File", "pos": [1559.2982177734375, -2375.762939453125], "size": [270, 174], "flags": {}, "order": 93, "mode": 0, "inputs": [{"label": "多行文本", "name": "multiline_text", "shape": 7, "type": "STRING", "link": 1960}, {"label": "index", "name": "index", "type": "INT", "widget": {"name": "index"}, "link": 1961}], "outputs": [{"label": "文本行", "name": "line_text", "type": "STRING", "links": [1968]}, {"label": "字典", "name": "dictionary", "type": "DICT", "links": null}], "properties": {"cnr_id": "was-ns", "ver": "3.0.0", "Node name for S&R": "Text Load Line From File", "widget_ue_connectable": {}}, "widgets_values": ["", "[filename]", "TextBatch", "index", 0]}, {"id": 1056, "type": "easy showAnything", "pos": [4367.9033203125, -2127.533935546875], "size": [210, 94.02214813232422], "flags": {}, "order": 120, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 1957}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["160"]}, {"id": 967, "type": "VHS_GetImageCount", "pos": [3990.302734375, -2076.8125], "size": [225.9013671875, 26], "flags": {"collapsed": true}, "order": 113, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1773}], "outputs": [{"label": "计数", "name": "count", "type": "INT", "links": [1757, 1957]}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1", "Node name for S&R": "VHS_GetImageCount", "widget_ue_connectable": {}}, "widgets_values": {}}, {"id": 962, "type": "SetNode", "pos": [4767.6552734375, -1903.049072265625], "size": [308.7657470703125, 58], "flags": {"collapsed": false}, "order": 133, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 1764}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": []}], "title": "Set_IMAGE", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["img_part2_frames_Control"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 952, "type": "SetNode", "pos": [4763.83447265625, -1620.884765625], "size": [303.2400817871094, 58], "flags": {"collapsed": false}, "order": 134, "mode": 0, "inputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "link": 1744}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": []}], "title": "Set_MASK", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["img_part2_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 1052, "type": "MathExpression|pysssss", "pos": [3591.536376953125, -1470.6131591796875], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 109, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1949}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1950}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1951}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1953]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a + (b * c)", [false, true]]}, {"id": 1054, "type": "MathExpression|pysssss", "pos": [3262.667724609375, -1456.7012939453125], "size": [210, 128], "flags": {"collapsed": false}, "order": 81, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1945}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1946}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1951]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a-b", [false, true]]}, {"id": 965, "type": "MathExpression|pysssss", "pos": [3380.155517578125, -1979.76416015625], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 97, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1922}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1924}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1925}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1770]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["ceil((a - b) / (b - c))", [false, true]]}, {"id": 1042, "type": "MathExpression|pysssss", "pos": [3062.802734375, -2058.42578125], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 79, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1920}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1921}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1922, 1952]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a*b", [false, true]]}, {"id": 982, "type": "GetNode", "pos": [2819.014892578125, -2063.75537109375], "size": [210, 58], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1920]}], "title": "Get_total_seconds", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["total_seconds"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 972, "type": "GetNode", "pos": [2830.20556640625, -2200.812255859375], "size": [210, 58], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1769, 1923]}], "title": "Get_video_part1", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["video_part1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 968, "type": "easy showAnything", "pos": [4114.74951171875, -2277.730712890625], "size": [210, 94.02214813232422], "flags": {}, "order": 108, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 1774}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["1"]}, {"id": 1044, "type": "VHS_GetImageCount", "pos": [3036.869384765625, -2266.976318359375], "size": [225.9013671875, 26], "flags": {"collapsed": false}, "order": 78, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1923}], "outputs": [{"label": "计数", "name": "count", "type": "INT", "links": [1924, 1971]}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1", "Node name for S&R": "VHS_GetImageCount", "widget_ue_connectable": {}}, "widgets_values": {}}, {"id": 1067, "type": "easy showAnything", "pos": [3295.646484375, -2349.890380859375], "size": [210, 94.02214813232422], "flags": {}, "order": 96, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 1971}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["81"]}, {"id": 983, "type": "GetNode", "pos": [2832.918701171875, -1894.7847900390625], "size": [210, 59.94718933105469], "flags": {"collapsed": false}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [1921]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["fps"], "color": "#232", "bgcolor": "#353"}, {"id": 949, "type": "GetNode", "pos": [2839.716064453125, -1730.754150390625], "size": [210, 58], "flags": {"collapsed": false}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1772, 1945, 1949, 1954]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1063, "type": "MathExpression|pysssss", "pos": [1756.6121826171875, -1733.2071533203125], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 110, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1970}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1963]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a + 1", [false, true]]}, {"id": 987, "type": "UpscalerTensorrt", "pos": [8505.2685546875, -2513.643310546875], "size": [270, 78], "flags": {}, "order": 164, "mode": 4, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1958}, {"label": "upscaler_trt_model", "name": "upscaler_trt_model", "type": "UPSCALER_TRT_MODEL", "link": 1818}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": []}], "properties": {"cnr_id": "ComfyUI-Upscaler-Tensorrt", "ver": "e0c94a8dac0310bb8d6add6838db4ed137106da8", "Node name for S&R": "UpscalerTensorrt", "widget_ue_connectable": {}}, "widgets_values": ["2k"]}, {"id": 986, "type": "LoadUpscalerTensorrtModel", "pos": [8503.7607421875, -2826.2275390625], "size": [281.9956970214844, 82], "flags": {}, "order": 25, "mode": 4, "inputs": [], "outputs": [{"label": "upscaler_trt_model", "name": "upscaler_trt_model", "type": "UPSCALER_TRT_MODEL", "links": [1818]}], "properties": {"cnr_id": "ComfyUI-Upscaler-Tensorrt", "ver": "e0c94a8dac0310bb8d6add6838db4ed137106da8", "Node name for S&R": "LoadUpscalerTensorrtModel", "widget_ue_connectable": {}}, "widgets_values": ["4x-AnimeSharp", "fp16"]}, {"id": 1039, "type": "RIFE VFI", "pos": [7935.14404296875, -2780.495361328125], "size": [360.1350402832031, 211.79824829101562], "flags": {}, "order": 162, "mode": 4, "inputs": [{"label": "图像", "name": "frames", "type": "IMAGE", "link": 1976}, {"label": "插值规则(可选)", "name": "optional_interpolation_states", "shape": 7, "type": "INTERPOLATION_STATES", "link": null}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [1918, 1958]}], "properties": {"cnr_id": "comfyui-frame-interpolation", "ver": "1.0.7", "Node name for S&R": "RIFE VFI", "widget_ue_connectable": {}}, "widgets_values": ["rife47.pth", 10, 2, true, true, 1]}, {"id": 1026, "type": "GetNode", "pos": [4886.71533203125, -3702.726318359375], "size": [210, 58], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [1877]}], "title": "Get_vae1111", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["vae1111"]}, {"id": 882, "type": "Note", "pos": [-924.2802734375, -3546.93994140625], "size": [535.257080078125, 472.5929260253906], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["--Model--\n\nWan2.1_vace\n(https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/tree/main/split_files/diffusion_models)\n\n\nWan2.1_T2V_14B_LightX2V_StepCfgDistill_VACE-GGUF\n(https://huggingface.co/QuantStack/Wan2.1_T2V_14B_LightX2V_StepCfgDistill_VACE-GGUF)\n\n\n--Text Encoder--\n(https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/tree/main/split_files/text_encoders)\n\n\n--VAE--\n(https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/tree/main/split_files/vae)\n\n\n--LORA--\n\nWan21_T2V_14B_MoviiGen_lora_rank32_fp16\n(https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_T2V_14B_MoviiGen_lora_rank32_fp16.safetensors)\n\nWan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32\n(https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors)\n \nWan2.1-Fun-14B-InP-MPS\n(https://huggingface.co/alibaba-pai/Wan2.1-Fun-Reward-LoRAs/blob/main/Wan2.1-Fun-14B-InP-MPS.safetensors)\n\nDetailEnhancerV1.safetensors\n(https://huggingface.co/vrgamedevgirl84/Wan14BT2VFusioniX/tree/main/OtherLoRa's)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 1058, "type": "Note", "pos": [-32.93220138549805, -3338.268798828125], "size": [277.3616638183594, 144.51341247558594], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["批次大小数值  请参照    81帧    +4  -4\n\n例如85   89  93      77  73   69等等"], "color": "#432", "bgcolor": "#653"}, {"id": 1068, "type": "Note", "pos": [1350.4129638671875, -1769.8389892578125], "size": [277.3616638183594, 144.51341247558594], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["第一行是初次采样的提示词，第二行是第一次循环的提示词，以此类推，可以让每次采样人物有不同的动作，如果视频只需要一行提示词，将第一行的提示词进行复制粘贴到第二行，第三行，填满即可\n\n根据设置的视频时长，和批次大小，合理的设计人物每个阶段的动作"], "color": "#432", "bgcolor": "#653"}, {"id": 900, "type": "GetNode", "pos": [367.13458251953125, -2205.900146484375], "size": [210, 50], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1675]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 901, "type": "GetNode", "pos": [382.29595947265625, -2060.581298828125], "size": [210, 50], "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1676]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1022, "type": "SetNode", "pos": [2324.75537109375, -3490.14404296875], "size": [210, 34], "flags": {"collapsed": true}, "order": 103, "mode": 0, "inputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "link": 2031}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_WANVIDEOMODEL", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 902, "type": "ImageResizeKJv2", "pos": [590.4777221679688, -2119.3076171875], "size": [270, 336], "flags": {}, "order": 91, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1988}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1675}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1676}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1989]}, {"label": "width", "name": "width", "type": "INT", "links": null}, {"label": "height", "name": "height", "type": "INT", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "ImageResizeKJv2", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [512, 512, "lanc<PERSON>s", "crop", "255,255,255", "center", 64, "cpu", "<tr><td>Output: </td><td><b>1</b> x <b>448</b> x <b>832 | 4.27MB</b></td></tr>"]}, {"id": 879, "type": "SetNode", "pos": [961.9618530273438, -2215.248291015625], "size": [210, 58], "flags": {"collapsed": true}, "order": 101, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 1989}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_IMAGE", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1066, "type": "WanVideoTextEmbedBridge", "pos": [3669.87109375, -2347.167724609375], "size": [222.00253295898438, 46], "flags": {}, "order": 132, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 2054}, {"label": "negative", "name": "negative", "shape": 7, "type": "CONDITIONING", "link": 2052}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "links": [1969]}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.2", "Node name for S&R": "WanVideoTextEmbedBridge", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1024, "type": "WanVideoExperimentalArgs", "pos": [4136.07470703125, -3229.52099609375], "size": [327.5999755859375, 226], "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"label": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [1875]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoExperimentalArgs", "widget_ue_connectable": {}}, "widgets_values": ["", true, false, 0, false, 1, 1.25, 20, false]}, {"id": 1078, "type": "WanVideoEnhanceAVideo", "pos": [4175.80419921875, -3603.89453125], "size": [277.0943298339844, 106], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"label": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [1992]}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.5", "Node name for S&R": "WanVideoEnhanceAVideo", "widget_ue_connectable": {}}, "widgets_values": [2, 0, 1]}, {"id": 1023, "type": "WanVideoSLG", "pos": [4238.9345703125, -3432.31201171875], "size": [315, 106], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"label": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [1874]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoSLG", "widget_ue_connectable": {}}, "widgets_values": ["8", 0.30000000000000004, 0.7000000000000002]}, {"id": 1025, "type": "WanVideoDecode", "pos": [4969.39111328125, -3364.5166015625], "size": [315, 198], "flags": {"collapsed": true}, "order": 135, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 1877}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 1876}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [1878, 1995]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 1040, "type": "VHS_VideoCombine", "pos": [8326.8125, -2167.403076171875], "size": [763.711181640625, 334], "flags": {}, "order": 163, "mode": 4, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1918}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {"frame_rate": true}}, "widgets_values": {"frame_rate": 32, "loop_count": 0, "filename_prefix": "wan_native", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "wan_native_00004.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 32, "workflow": "wan_native_00004.png", "fullpath": "G:\\ComfyUI\\ComfyUI\\output\\wan_native_00004.mp4"}, "muted": true}}}, {"id": 1036, "type": "LayerFilter: AddGrain", "pos": [7318.5625, -2799.0556640625], "size": [213.01808166503906, 106], "flags": {"collapsed": false}, "order": 160, "mode": 4, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1997}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [1976]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerFilter: AddGrain", "widget_ue_connectable": {}}, "widgets_values": [0.4300000000000001, 0.5, 0.5000000000000001], "color": "rgba(34, 67, 111, 0.7)"}, {"id": 992, "type": "GetNode", "pos": [6787.26708984375, -2183.7275390625], "size": [210, 58], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [2071]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["fps"], "color": "#232", "bgcolor": "#353"}, {"id": 991, "type": "Reroute", "pos": [6567.748046875, -2249.273193359375], "size": [75, 26], "flags": {}, "order": 158, "mode": 0, "inputs": [{"label": "", "name": "", "type": "*", "link": 1832}], "outputs": [{"label": "", "name": "", "type": "*", "links": [1997, 2072]}], "properties": {"showOutputText": false, "horizontal": false, "widget_ue_connectable": {}}}, {"id": 960, "type": "MathExpression|pysssss", "pos": [5601.97802734375, -1269.3599853515625], "size": [210, 128], "flags": {"collapsed": false}, "order": 123, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1956}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1936}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1763]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a-b", [false, true]]}, {"id": 951, "type": "MathExpression|pysssss", "pos": [5355.48828125, -1250.5589599609375], "size": [210, 128], "flags": {"collapsed": false}, "order": 80, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1743}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1762, 1936]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a-1", [false, true]]}, {"id": 850, "type": "GetNode", "pos": [5190.6220703125, -1334.8336181640625], "size": [210, 58], "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1743]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["int_overlap_frames"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1051, "type": "easy showAnything", "pos": [5095.8544921875, -1224.089111328125], "size": [210, 94.02214813232422], "flags": {}, "order": 149, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 1940}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["81"]}, {"id": 894, "type": "LayerUtility: PurgeVRAM", "pos": [4886.2783203125, -1075.633056640625], "size": [210, 82], "flags": {"collapsed": true}, "order": 141, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 1903}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [false, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 1050, "type": "VHS_GetImageCount", "pos": [4844.06689453125, -1204.3319091796875], "size": [225.9013671875, 26], "flags": {"collapsed": false}, "order": 142, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1939}], "outputs": [{"label": "计数", "name": "count", "type": "INT", "links": [1940]}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1", "Node name for S&R": "VHS_GetImageCount", "widget_ue_connectable": {}}, "widgets_values": {}}, {"id": 1083, "type": "CR Image Input Switch (4 way)", "pos": [6243.744140625, -630.3760986328125], "size": [270, 118], "flags": {}, "order": 151, "mode": 0, "inputs": [{"label": "图像1", "name": "image1", "shape": 7, "type": "IMAGE", "link": 2000}, {"label": "图像2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 2001}, {"label": "图像3", "name": "image3", "shape": 7, "type": "IMAGE", "link": 2002}, {"label": "图像4", "name": "image4", "shape": 7, "type": "IMAGE", "link": 2003}, {"label": "Input", "name": "Input", "type": "INT", "widget": {"name": "Input"}, "link": 2004}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2011]}, {"label": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Image Input Switch (4 way)", "widget_ue_connectable": {}}, "widgets_values": [1]}, {"id": 1085, "type": "CR Image Input Switch (4 way)", "pos": [6241.3662109375, -864.5393676757812], "size": [270, 118], "flags": {}, "order": 150, "mode": 0, "inputs": [{"label": "图像1", "name": "image1", "shape": 7, "type": "IMAGE", "link": 2005}, {"label": "图像2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 2006}, {"label": "图像3", "name": "image3", "shape": 7, "type": "IMAGE", "link": 2007}, {"label": "图像4", "name": "image4", "shape": 7, "type": "IMAGE", "link": 2008}, {"label": "Input", "name": "Input", "type": "INT", "widget": {"name": "Input"}, "link": 2009}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2012]}, {"label": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Image Input Switch (4 way)", "widget_ue_connectable": {}}, "widgets_values": [1]}, {"id": 961, "type": "ImageFromBatch", "pos": [5863.58837890625, -1371.2864990234375], "size": [270, 82], "flags": {}, "order": 154, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 1761}, {"label": "batch_index", "name": "batch_index", "type": "INT", "widget": {"name": "batch_index"}, "link": 1762}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 1763}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [1776]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageFromBatch", "widget_ue_connectable": {"batch_index": true, "length": true}}, "widgets_values": [0, 1]}, {"id": 993, "type": "LayerUtility: PurgeVRAM", "pos": [6678.8310546875, -1570.103759765625], "size": [210, 82], "flags": {"collapsed": true}, "order": 159, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 1833}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 971, "type": "easy forLoopEnd", "pos": [6564.92919921875, -1431.4989013671875], "size": [196.2582550048828, 81.0861587524414], "flags": {}, "order": 156, "mode": 0, "inputs": [{"label": "流", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "link": 1778}, {"label": "初始值1", "name": "initial_value1", "shape": 7, "type": "*", "link": 1779}, {"label": "initial_value2", "name": "initial_value2", "type": "*", "link": null}], "outputs": [{"label": "值1", "name": "value1", "type": "*", "links": [1832, 1833]}, {"label": "value2", "name": "value2", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy forLoopEnd", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 950, "type": "SetNode", "pos": [5240.65673828125, -1051.669677734375], "size": [210, 58], "flags": {}, "order": 153, "mode": 0, "inputs": [{"label": "输入", "name": "*", "type": "*", "link": 2013}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": [1761]}], "title": "Set_*", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["video_part2"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1087, "type": "LogicGateEither", "pos": [6733.64111328125, -936.1984252929688], "size": [147.81210327148438, 66], "flags": {}, "order": 152, "mode": 0, "inputs": [{"label": "condition", "name": "condition", "type": "*", "link": 2010}, {"label": "input1", "name": "input1", "type": "*", "link": 2011}, {"label": "input2", "name": "input2", "type": "*", "link": 2012}], "outputs": [{"label": "*", "name": "*", "type": "*", "links": [2013]}], "properties": {"cnr_id": "comfyui-logicutils", "ver": "1.7.2", "Node name for S&R": "LogicGateEither", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1034, "type": "WanVideoDecode", "pos": [4712.0869140625, -819.5648193359375], "size": [315, 198], "flags": {"collapsed": true}, "order": 139, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 1901}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 1900}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [1903, 1939, 2014, 2015, 2016, 2017, 2018]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 875, "type": "GetNode", "pos": [3957.4287109375, -826.7650756835938], "size": [210, 58], "flags": {"collapsed": true}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1885]}], "title": "Get_steps", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 884, "type": "GetNode", "pos": [3989.934814453125, -708.1051635742188], "size": [210, 58], "flags": {"collapsed": true}, "order": 38, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1886]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1091, "type": "WanVideoEnhanceAVideo", "pos": [4016.82080078125, -1142.5220947265625], "size": [277.0943298339844, 106], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [], "outputs": [{"label": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [2019]}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.5", "Node name for S&R": "WanVideoEnhanceAVideo", "widget_ue_connectable": {}}, "widgets_values": [2, 0, 1]}, {"id": 966, "type": "easy forLoopStart", "pos": [3560.135009765625, -2251.256103515625], "size": [270, 158], "flags": {}, "order": 105, "mode": 0, "inputs": [{"label": "初始值1", "name": "initial_value1", "shape": 7, "type": "*", "link": 1769}, {"label": "total", "name": "total", "type": "INT", "widget": {"name": "total"}, "link": 1770}, {"label": "initial_value3", "name": "initial_value3", "type": "*", "link": 1772}, {"label": "initial_value4", "name": "initial_value4", "type": "*", "link": null}], "outputs": [{"label": "流", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "links": [1778]}, {"label": "索引", "name": "index", "type": "INT", "links": [1774, 1950, 1970, 2021, 2022]}, {"label": "值1", "name": "value1", "type": "*", "links": [1752, 1773, 1775]}, {"label": "value3", "name": "value3", "type": "*", "links": null}, {"label": "value4", "name": "value4", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy forLoopStart", "widget_ue_connectable": {"total": true}}, "widgets_values": [1], "color": "#223", "bgcolor": "#335"}, {"id": 1090, "type": "MathExpression|pysssss", "pos": [6093.16259765625, -1049.738525390625], "size": [210, 128], "flags": {"collapsed": false}, "order": 112, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 2022}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [2010]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a > 4", [false, true]]}, {"id": 1089, "type": "MathExpression|pysssss", "pos": [5787.0556640625, -714.83740234375], "size": [210, 128], "flags": {"collapsed": false}, "order": 111, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 2021}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [2004, 2009]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["(a % 4) + 1", [false, true]]}, {"id": 978, "type": "SetNode", "pos": [677.3570556640625, -2752.093994140625], "size": [210, 50], "flags": {"collapsed": true}, "order": 88, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1802}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "total_seconds", "widget_ue_connectable": {}}, "widgets_values": ["total_seconds"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1102, "type": "GetNode", "pos": [2267.66796875, -4548.76171875], "size": [210, 34], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [2029]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1101, "type": "GetNode", "pos": [2276.53662109375, -4445.97900390625], "size": [210, 34], "flags": {"collapsed": true}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [2030]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1077, "type": "WanVideoSetRadialAttention", "pos": [1957.52294921875, -3372.63427734375], "size": [291.3540954589844, 178], "flags": {}, "order": 102, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 1990}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "links": []}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.5", "Node name for S&R": "WanVideoSetRadialAttention", "widget_ue_connectable": {}}, "widgets_values": ["sageattn", 1, 1, 1, 0.2, 64]}, {"id": 942, "type": "GetNode", "pos": [2962.489013671875, -3493.012451171875], "size": [210, 58], "flags": {"collapsed": true}, "order": 42, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1867]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 980, "type": "WanVideoVACEStartToEndFrame", "pos": [3195.434326171875, -3487.553466796875], "size": [329.9634704589844, 190], "flags": {}, "order": 71, "mode": 4, "inputs": [{"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": null}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE", "link": null}, {"label": "control_images", "name": "control_images", "shape": 7, "type": "IMAGE", "link": null}, {"label": "inpaint_mask", "name": "inpaint_mask", "shape": 7, "type": "MASK", "link": null}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1809}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": []}, {"label": "masks", "name": "masks", "type": "MASK", "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "9e8978731b8e653664d10d258eb1792a46f2bf17", "Node name for S&R": "WanVideoVACEStartToEndFrame", "aux_id": "MaTeZZ/ComfyUI-WanVideoWrapper-MultiTalk", "widget_ue_connectable": {"num_frames": true}}, "widgets_values": [129, 0.5, 0, -1]}, {"id": 1018, "type": "WanVideoVACEEncode", "pos": [3801.38720703125, -3522.404541015625], "size": [264.6615905761719, 282], "flags": {}, "order": 121, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 1869}, {"label": "input_frames", "name": "input_frames", "shape": 7, "type": "IMAGE", "link": 2035}, {"label": "ref_images", "name": "ref_images", "shape": 7, "type": "IMAGE", "link": 1867}, {"label": "input_masks", "name": "input_masks", "shape": 7, "type": "MASK", "link": null}, {"label": "prev_vace_embeds", "name": "prev_vace_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": null}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1889}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1890}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1891}], "outputs": [{"label": "vace_embeds", "name": "vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [2055]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVACEEncode", "widget_ue_connectable": {"width": true, "height": true, "num_frames": true}}, "widgets_values": [480, 832, 29, 1.0000000000000002, 0, 1, false], "color": "#322", "bgcolor": "#533"}, {"id": 945, "type": "SetNode", "pos": [5566.06298828125, -3602.876708984375], "size": [210, 58], "flags": {"collapsed": true}, "order": 140, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 1996}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [2036]}], "title": "Set_IMAGE", "properties": {"previousName": "video_part1", "widget_ue_connectable": {}}, "widgets_values": ["video_part1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 994, "type": "GetNode", "pos": [5446.02880859375, -3865.179931640625], "size": [210, 58], "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [2037]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["fps"], "color": "#232", "bgcolor": "#353"}, {"id": 1079, "type": "WanVideoEasyCache", "pos": [4192.703125, -3966.02685546875], "size": [270, 130], "flags": {}, "order": 44, "mode": 0, "inputs": [], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [1993]}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.5", "Node name for S&R": "WanVideoEasyCache", "widget_ue_connectable": {}}, "widgets_values": [0.015, 1, -1, "offload_device"]}, {"id": 956, "type": "GetImageRangeFromBatch", "pos": [3902.2412109375, -1896.911376953125], "size": [340.3267517089844, 102], "flags": {"collapsed": false}, "order": 125, "mode": 0, "inputs": [{"label": "图像", "name": "images", "shape": 7, "type": "IMAGE", "link": 1752}, {"label": "遮罩", "name": "masks", "shape": 7, "type": "MASK", "link": null}, {"label": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": 1753}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1754}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [1745]}, {"label": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {"start_index": true, "num_frames": true}}, "widgets_values": [0, 7]}, {"id": 959, "type": "GetNode", "pos": [2852.759765625, -1526.5537109375], "size": [210, 58], "flags": {"collapsed": false}, "order": 45, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1754, 1758, 1925, 1946]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["int_overlap_frames"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 953, "type": "WanVideoVACEStartToEndFrame", "pos": [4326.83203125, -1806.1893310546875], "size": [329.9634704589844, 190], "flags": {}, "order": 130, "mode": 0, "inputs": [{"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 1745}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE", "link": null}, {"label": "control_images", "name": "control_images", "shape": 7, "type": "IMAGE", "link": 2045}, {"label": "inpaint_mask", "name": "inpaint_mask", "shape": 7, "type": "MASK", "link": null}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 2041}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [1764]}, {"label": "masks", "name": "masks", "type": "MASK", "links": [1744]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "9e8978731b8e653664d10d258eb1792a46f2bf17", "Node name for S&R": "WanVideoVACEStartToEndFrame", "aux_id": "MaTeZZ/ComfyUI-WanVideoWrapper-MultiTalk", "widget_ue_connectable": {"num_frames": true}}, "widgets_values": [129, 0.5, 0, -1]}, {"id": 1081, "type": "EsesImageEffectLevels", "pos": [5543.8056640625, -809.5767822265625], "size": [210, 862.7626953125], "flags": {"collapsed": true}, "order": 145, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 2016}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "adjusted_image", "name": "adjusted_image", "type": "IMAGE", "links": [2007]}, {"label": "adjusted_mask", "name": "adjusted_mask", "type": "MASK", "links": null}, {"label": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"aux_id": "quasiblob/ComfyUI-EsesImageEffectLevels", "ver": "1db7439321f785e71d05d3c5eeb3d017f6edfea6", "Node name for S&R": "EsesImageEffectLevels", "widget_ue_connectable": {}}, "widgets_values": ["None", "RGB", "{\"rgb\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1.09,\"output_black\":9,\"output_white\":255},\"r\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.94,\"output_black\":0,\"output_white\":255},\"g\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.94,\"output_black\":0,\"output_white\":255},\"b\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.94,\"output_black\":0,\"output_white\":255},\"mask\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1,\"output_black\":0,\"output_white\":255}}", false, 0, 0.05, 0, 1.09, 1, 9, 255, null, null, null, null, null], "custom_all_levels": {"rgb": {"black_point": 0, "white_point": 1, "mid_point": 1.09, "output_black": 9, "output_white": 255}, "r": {"black_point": 0, "white_point": 1, "mid_point": 0.94, "output_black": 0, "output_white": 255}, "g": {"black_point": 0, "white_point": 1, "mid_point": 0.94, "output_black": 0, "output_white": 255}, "b": {"black_point": 0, "white_point": 1, "mid_point": 0.94, "output_black": 0, "output_white": 255}, "mask": {"black_point": 0, "white_point": 1, "mid_point": 1, "output_black": 0, "output_white": 255}}}, {"id": 1086, "type": "EsesImageEffectLevels", "pos": [5543.1357421875, -750.2319946289062], "size": [210, 862.7626953125], "flags": {"collapsed": true}, "order": 146, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 2017}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "adjusted_image", "name": "adjusted_image", "type": "IMAGE", "links": [2008]}, {"label": "adjusted_mask", "name": "adjusted_mask", "type": "MASK", "links": null}, {"label": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"aux_id": "quasiblob/ComfyUI-EsesImageEffectLevels", "ver": "1db7439321f785e71d05d3c5eeb3d017f6edfea6", "Node name for S&R": "EsesImageEffectLevels", "widget_ue_connectable": {}}, "widgets_values": ["None", "RGB", "{\"rgb\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1.11,\"output_black\":11,\"output_white\":255},\"r\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.92,\"output_black\":0,\"output_white\":255},\"g\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.92,\"output_black\":0,\"output_white\":255},\"b\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.92,\"output_black\":0,\"output_white\":255},\"mask\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1,\"output_black\":0,\"output_white\":255}}", false, 0, 0.05, 0, 1.11, 1, 11, 255, null, null, null, null, null], "custom_all_levels": {"rgb": {"black_point": 0, "white_point": 1, "mid_point": 1.11, "output_black": 11, "output_white": 255}, "r": {"black_point": 0, "white_point": 1, "mid_point": 0.92, "output_black": 0, "output_white": 255}, "g": {"black_point": 0, "white_point": 1, "mid_point": 0.92, "output_black": 0, "output_white": 255}, "b": {"black_point": 0, "white_point": 1, "mid_point": 0.92, "output_black": 0, "output_white": 255}, "mask": {"black_point": 0, "white_point": 1, "mid_point": 1, "output_black": 0, "output_white": 255}}}, {"id": 1082, "type": "EsesImageEffectLevels", "pos": [5541.72265625, -880.2672729492188], "size": [210, 862.7626953125], "flags": {"collapsed": true}, "order": 144, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 2015}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "adjusted_image", "name": "adjusted_image", "type": "IMAGE", "links": [2006]}, {"label": "adjusted_mask", "name": "adjusted_mask", "type": "MASK", "links": null}, {"label": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"aux_id": "quasiblob/ComfyUI-EsesImageEffectLevels", "ver": "1db7439321f785e71d05d3c5eeb3d017f6edfea6", "Node name for S&R": "EsesImageEffectLevels", "widget_ue_connectable": {}}, "widgets_values": ["None", "RGB", "{\"rgb\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1.07,\"output_black\":7,\"output_white\":255},\"r\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.96,\"output_black\":0,\"output_white\":255},\"g\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.96,\"output_black\":0,\"output_white\":255},\"b\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.96,\"output_black\":0,\"output_white\":255},\"mask\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1,\"output_black\":0,\"output_white\":255}}", false, 0, 0.05, 0, 1.07, 1, 7, 255, null, null, null, null, null], "custom_all_levels": {"rgb": {"black_point": 0, "white_point": 1, "mid_point": 1.07, "output_black": 7, "output_white": 255}, "r": {"black_point": 0, "white_point": 1, "mid_point": 0.96, "output_black": 0, "output_white": 255}, "g": {"black_point": 0, "white_point": 1, "mid_point": 0.96, "output_black": 0, "output_white": 255}, "b": {"black_point": 0, "white_point": 1, "mid_point": 0.96, "output_black": 0, "output_white": 255}, "mask": {"black_point": 0, "white_point": 1, "mid_point": 1, "output_black": 0, "output_white": 255}}}, {"id": 1016, "type": "WanVideoTextEmbedBridge", "pos": [3840.883544921875, -2923.4609375], "size": [222.00253295898438, 46], "flags": {}, "order": 116, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 2048}, {"label": "negative", "name": "negative", "shape": 7, "type": "CONDITIONING", "link": 2050}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "links": [1873]}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.2", "Node name for S&R": "WanVideoTextEmbedBridge", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1100, "type": "ImageResizeKJv2", "pos": [2643.0185546875, -3654.52783203125], "size": [270, 336], "flags": {}, "order": 114, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 2028}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 2029}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 2030}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [2035, 2044]}, {"label": "width", "name": "width", "type": "INT", "links": null}, {"label": "height", "name": "height", "type": "INT", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJv2", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [512, 363, "nearest-exact", "crop", "0, 0, 0", "center", 64, "cpu", "<tr><td>Output: </td><td><b>232</b> x <b>448</b> x <b>832 | 989.62MB</b></td></tr>"]}, {"id": 1108, "type": "easy cleanGpuUsed", "pos": [2967.109130859375, -2925.651611328125], "size": [140, 26], "flags": {}, "order": 107, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2047}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2048]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1109, "type": "easy cleanGpuUsed", "pos": [3136.451171875, -2702.99755859375], "size": [140, 26], "flags": {}, "order": 94, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2049}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2050]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1110, "type": "easy cleanGpuUsed", "pos": [3122.339111328125, -2485.0478515625], "size": [140, 26], "flags": {}, "order": 95, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2051}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2052]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1111, "type": "easy cleanGpuUsed", "pos": [2978.084716796875, -2406.648681640625], "size": [140, 26], "flags": {}, "order": 129, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2053}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2054]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1112, "type": "easy cleanGpuUsed", "pos": [4179.66162109375, -3519.1328125], "size": [140, 26], "flags": {}, "order": 127, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2055}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2056]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1113, "type": "easy cleanGpuUsed", "pos": [3391.49560546875, -829.600341796875], "size": [140, 26], "flags": {"collapsed": true}, "order": 76, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2057}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2058]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1114, "type": "easy cleanGpuUsed", "pos": [3436.634765625, -1137.610107421875], "size": [140, 26], "flags": {"collapsed": true}, "order": 74, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2059}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2060]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1028, "type": "WanVideoVACEEncode", "pos": [3552.862548828125, -1087.004638671875], "size": [264.6615905761719, 282], "flags": {}, "order": 122, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 1888}, {"label": "input_frames", "name": "input_frames", "shape": 7, "type": "IMAGE", "link": 2060}, {"label": "ref_images", "name": "ref_images", "shape": 7, "type": "IMAGE", "link": 1895}, {"label": "input_masks", "name": "input_masks", "shape": 7, "type": "MASK", "link": 2058}, {"label": "prev_vace_embeds", "name": "prev_vace_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": null}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1892}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1893}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1955}], "outputs": [{"label": "vace_embeds", "name": "vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [2061]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVACEEncode", "widget_ue_connectable": {"width": true, "height": true, "num_frames": true}}, "widgets_values": [480, 832, 29, 1.0000000000000002, 0, 1, false], "color": "#322", "bgcolor": "#533"}, {"id": 1115, "type": "easy cleanGpuUsed", "pos": [3910.72021484375, -1024.7021484375], "size": [140, 26], "flags": {"collapsed": true}, "order": 128, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "type": "*", "link": 2061}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [2062]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67", "Node name for S&R": "easy cleanGpuUsed", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 923, "type": "LayerUtility: PurgeVRAM", "pos": [5034.6298828125, -3608.69482421875], "size": [270, 82], "flags": {"collapsed": true}, "order": 137, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 1878}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [false, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 1116, "type": "LayerUtility: PurgeVRAM", "pos": [2470.533447265625, -4296.90576171875], "size": [270, 82], "flags": {"collapsed": true}, "order": 115, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 2063}], "outputs": [], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [false, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 1099, "type": "ImageBlend", "pos": [2076.570556640625, -4366.31103515625], "size": [270, 102], "flags": {}, "order": 106, "mode": 0, "inputs": [{"label": "图像1", "name": "image1", "type": "IMAGE", "link": 2026}, {"label": "图像2", "name": "image2", "type": "IMAGE", "link": 2027}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2028, 2063]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ImageBlend", "widget_ue_connectable": {}}, "widgets_values": [0.10000000000000002, "normal"]}, {"id": 1084, "type": "EsesImageEffectLevels", "pos": [5529.4609375, -604.1818237304688], "size": [210, 862.7626953125], "flags": {"collapsed": true}, "order": 147, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 2018}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "adjusted_image", "name": "adjusted_image", "type": "IMAGE", "links": [2000, 2001, 2002, 2003]}, {"label": "adjusted_mask", "name": "adjusted_mask", "type": "MASK", "links": null}, {"label": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"aux_id": "quasiblob/ComfyUI-EsesImageEffectLevels", "ver": "1db7439321f785e71d05d3c5eeb3d017f6edfea6", "Node name for S&R": "EsesImageEffectLevels", "widget_ue_connectable": {}}, "widgets_values": ["None", "RGB", "{\"rgb\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1.11,\"output_black\":11,\"output_white\":255},\"r\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.92,\"output_black\":0,\"output_white\":255},\"g\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.92,\"output_black\":0,\"output_white\":255},\"b\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.92,\"output_black\":0,\"output_white\":255},\"mask\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1,\"output_black\":0,\"output_white\":255}}", false, 0, 0.05, 0, 1.11, 1, 11, 255, null, null, null, null, null], "custom_all_levels": {"rgb": {"black_point": 0, "white_point": 1, "mid_point": 1.11, "output_black": 11, "output_white": 255}, "r": {"black_point": 0, "white_point": 1, "mid_point": 0.92, "output_black": 0, "output_white": 255}, "g": {"black_point": 0, "white_point": 1, "mid_point": 0.92, "output_black": 0, "output_white": 255}, "b": {"black_point": 0, "white_point": 1, "mid_point": 0.92, "output_black": 0, "output_white": 255}, "mask": {"black_point": 0, "white_point": 1, "mid_point": 1, "output_black": 0, "output_white": 255}}}, {"id": 1106, "type": "GetImageRangeFromBatch", "pos": [3906.4716796875, -1730.5885009765625], "size": [340.3267517089844, 102], "flags": {"collapsed": false}, "order": 126, "mode": 0, "inputs": [{"label": "图像", "name": "images", "shape": 7, "type": "IMAGE", "link": 2044}, {"label": "遮罩", "name": "masks", "shape": 7, "type": "MASK", "link": null}, {"label": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": 2043}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 2064}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2045]}, {"label": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {"start_index": true, "num_frames": true}}, "widgets_values": [0, 7]}, {"id": 958, "type": "MathExpression|pysssss", "pos": [3578.896728515625, -1714.13623046875], "size": [210, 128], "flags": {"collapsed": false}, "order": 119, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1757}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1758}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1753, 2043]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a-b", [false, true]]}, {"id": 1055, "type": "MathExpression|pysssss", "pos": [3917.607666015625, -1474.1099853515625], "size": [210, 128], "flags": {"collapsed": false}, "order": 117, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1952}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1953}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1954}], "outputs": [{"label": "整数", "name": "INT", "type": "INT", "links": [1955, 1956, 2041, 2064]}, {"label": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["min(c, a - b)", [false, true]]}, {"id": 1095, "type": "GetNode", "pos": [883.3399658203125, -4616.80712890625], "size": [210, 58], "flags": {}, "order": 46, "mode": 0, "inputs": [], "outputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "links": [2065]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["fps"], "color": "#232", "bgcolor": "#353"}, {"id": 1117, "type": "VHS_VideoCombine", "pos": [7692.40478515625, -4425.74169921875], "size": [594.4888305664062, 1343.7806396484375], "flags": {}, "order": 47, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00006-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00006.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00006-audio.mp4"}}}}, {"id": 1118, "type": "VHS_VideoCombine", "pos": [8546.4814453125, -4450.57958984375], "size": [594.4888305664062, 1343.7806396484375], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00007-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00007.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00007-audio.mp4"}}}}, {"id": 1104, "type": "Reroute", "pos": [5163.46923828125, -2603.036865234375], "size": [75, 26], "flags": {}, "order": 100, "mode": 0, "inputs": [{"label": "", "name": "", "type": "*", "link": 2033}], "outputs": [{"label": "", "name": "", "type": "AUDIO", "links": [2038, 2068, 2073]}], "properties": {"showOutputText": false, "horizontal": false, "widget_ue_connectable": {}}}, {"id": 969, "type": "ImageBatchMulti", "pos": [6201.40234375, -1411.5499267578125], "size": [270, 102], "flags": {}, "order": 155, "mode": 0, "inputs": [{"label": "图像_1", "name": "image_1", "type": "IMAGE", "link": 1775}, {"label": "图像_2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 1776}], "outputs": [{"label": "图像", "name": "images", "type": "IMAGE", "links": [1779, 2070]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "widget_ue_connectable": {}}, "widgets_values": [2, null]}, {"id": 1119, "type": "VHS_VideoCombine", "pos": [5671.541015625, -2443.822998046875], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 157, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 2070}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": 2068}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"label": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": 2069}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00042-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00042.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00042-audio.mp4"}}}}, {"id": 1121, "type": "VHS_VideoCombine", "pos": [9880.40234375, -2020.841796875], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00009-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00009.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00009-audio.mp4"}}}}, {"id": 922, "type": "SetNode", "pos": [606.0026245117188, -2568.90966796875], "size": [210, 50], "flags": {"collapsed": true}, "order": 89, "mode": 0, "inputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "link": 1698}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_FLOAT", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["fps"], "color": "#232", "bgcolor": "#353"}, {"id": 1105, "type": "VHS_VideoCombine", "pos": [5922.97412109375, -4288.2734375], "size": [594.4888305664062, 1343.7806396484375], "flags": {}, "order": 148, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 2036}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": 2038}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"label": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": 2037}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00041-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00041.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00041-audio.mp4"}}}}, {"id": 915, "type": "GetNode", "pos": [3745.657470703125, -3146.443603515625], "size": [210, 58], "flags": {"collapsed": true}, "order": 50, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [2074]}], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1123, "type": "VHS_VideoCombine", "pos": [10399.4287109375, -1976.035888671875], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00028-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00028.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00028-audio.mp4"}}}}, {"id": 1122, "type": "VHS_VideoCombine", "pos": [9508.962890625, -2027.9034423828125], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 52, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00015-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00015.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00015-audio.mp4"}}}}, {"id": 1124, "type": "VHS_VideoCombine", "pos": [10763.49609375, -1970.2750244140625], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 53, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00031-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00031.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00031-audio.mp4"}}}}, {"id": 1125, "type": "VHS_VideoCombine", "pos": [9472.46875, -999.5598754882812], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00034-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00034.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00034-audio.mp4"}}}}, {"id": 1126, "type": "VHS_VideoCombine", "pos": [9850.9248046875, -1007.81396484375], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 55, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00037-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00037.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00037-audio.mp4"}}}}, {"id": 1127, "type": "VHS_VideoCombine", "pos": [10386.642578125, -996.753173828125], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 56, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00040-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00040.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00040-audio.mp4"}}}}, {"id": 1120, "type": "VHS_VideoCombine", "pos": [7308.38427734375, -2251.649169921875], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 161, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 2072}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": 2073}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"label": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": 2071}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00043-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00043.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00043-audio.mp4"}}}}, {"id": 1128, "type": "VHS_VideoCombine", "pos": [10747.89453125, -1002.6790161132812], "size": [348.276611328125, 917.0128173828125], "flags": {}, "order": 57, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": null}, {"label": "音频", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "文件名", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 864.75, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00043-audio.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4", "frame_rate": 20, "workflow": "AnimateDiff_00043.png", "fullpath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\latentsync_bf21681e\\AnimateDiff_00043-audio.mp4"}}}}, {"id": 1096, "type": "VHS_LoadVideo", "pos": [1313.154296875, -4597.1201171875], "size": [247.455078125, 727.9202270507812], "flags": {}, "order": 82, "mode": 0, "inputs": [{"label": "批次管理", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"label": "force_rate", "name": "force_rate", "type": "FLOAT", "widget": {"name": "force_rate"}, "link": 2065}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2024, 2025]}, {"label": "帧计数", "name": "frame_count", "type": "INT", "links": []}, {"label": "音频", "name": "audio", "type": "AUDIO", "links": [2033]}, {"label": "视频信息", "name": "video_info", "type": "VHS_VIDEOINFO", "links": []}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_LoadVideo", "widget_ue_connectable": {}}, "widgets_values": {"video": "SaveTik.co_7522442023505333511-hd.mp4", "force_rate": 20, "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "format": "AnimateDiff", "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"filename": "SaveTik.co_7522442023505333511-hd.mp4", "type": "input", "format": "video/mp4", "force_rate": 20, "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1}}}}, {"id": 1010, "type": "WanVideoLoraSelect", "pos": [894.5301513671875, -3367.477783203125], "size": [503.4073486328125, 150], "flags": {}, "order": 58, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA", "link": null}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS", "link": null}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [1859]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "058286fc0f3b0651a2f6b68309df3f06e8332cc0", "Node name for S&R": "WanVideoLoraSelect", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_FusionX\\Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1.2000000000000002, false, false], "color": "#223", "bgcolor": "#335"}, {"id": 946, "type": "INTConstant", "pos": [292.1623229980469, -3545.456298828125], "size": [297.22064208984375, 58], "flags": {}, "order": 59, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "INT", "links": [1626]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [480], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 944, "type": "INTConstant", "pos": [280.25177001953125, -3319.078125], "size": [307.0483093261719, 60.4569206237793], "flags": {}, "order": 60, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "INT", "links": [1628]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [81], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 927, "type": "INTConstant", "pos": [291.9486083984375, -3196.823486328125], "size": [303.8179931640625, 58], "flags": {}, "order": 61, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "INT", "links": [1627]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [3], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 853, "type": "SetNode", "pos": [654.653076171875, -3181.194580078125], "size": [210, 50], "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1627}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["int_overlap_frames"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 998, "type": "easy seed", "pos": [338.0675354003906, -2956.7060546875], "size": [250, 130], "flags": {"collapsed": false}, "order": 62, "mode": 0, "inputs": [], "outputs": [{"label": "seed", "name": "seed", "type": "INT", "slot_index": 0, "links": [1834]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "041f49540cf66d8bb72530cbb9760a8eece7d0ab", "Node name for S&R": "easy seed", "widget_ue_connectable": {}}, "widgets_values": [593232638661841, "fixed", ""], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 975, "type": "INTConstant", "pos": [312.72235107421875, -3083.228759765625], "size": [298.4490966796875, 58], "flags": {}, "order": 63, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "INT", "links": [1658]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [4], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 883, "type": "SetNode", "pos": [676.904541015625, -3052.85205078125], "size": [210, 50], "flags": {"collapsed": true}, "order": 87, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 1658}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_INT", "properties": {"previousName": "steps", "widget_ue_connectable": {}}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 979, "type": "INTConstant", "pos": [320.9119873046875, -2762.233642578125], "size": [298.4490966796875, 58], "flags": {}, "order": 64, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "INT", "links": [1802]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "INTConstant", "widget_ue_connectable": {}}, "widgets_values": [11], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 928, "type": "FloatConstant", "pos": [330.82623291015625, -2597.916748046875], "size": [210, 58], "flags": {}, "order": 65, "mode": 0, "inputs": [], "outputs": [{"label": "值", "name": "value", "type": "FLOAT", "links": [1698]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "Node name for S&R": "FloatConstant", "widget_ue_connectable": {}}, "widgets_values": [20.000000000000004], "color": "#232", "bgcolor": "#353"}, {"id": 1069, "type": "LoadAndResizeImage", "pos": [-145.20184326171875, -2379.193359375], "size": [488.8644714355469, 695.2137451171875], "flags": {}, "order": 66, "mode": 0, "inputs": [], "outputs": [{"label": "图像", "name": "image", "type": "IMAGE", "links": [1987, 1988]}, {"label": "遮罩", "name": "mask", "type": "MASK", "links": null}, {"label": "宽度", "name": "width", "type": "INT", "links": []}, {"label": "高度", "name": "height", "type": "INT", "links": []}, {"label": "image_path", "name": "image_path", "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "d57154c3a808b8a3f232ed293eaa2d000867c884", "Node name for S&R": "LoadAndResizeImage", "widget_ue_connectable": {}, "aux_id": "kijai/ComfyUI-KJNodes"}, "widgets_values": ["ComfyUI0801_0006.png", false, 720, 1280, 1, true, 16, "alpha", "", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 1070, "type": "PreviewImage", "pos": [972.7650756835938, -2050.5458984375], "size": [185.6140594482422, 246], "flags": {}, "order": 90, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 1987}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 1080, "type": "EsesImageEffectLevels", "pos": [5270.568359375, -3768.162841796875], "size": [210, 862.7626953125], "flags": {}, "order": 138, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 1995}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "adjusted_image", "name": "adjusted_image", "type": "IMAGE", "links": [1996]}, {"label": "adjusted_mask", "name": "adjusted_mask", "type": "MASK", "links": null}, {"label": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"aux_id": "quasiblob/ComfyUI-EsesImageEffectLevels", "ver": "1db7439321f785e71d05d3c5eeb3d017f6edfea6", "Node name for S&R": "EsesImageEffectLevels", "widget_ue_connectable": {}}, "widgets_values": ["None", "RGB", "{\"rgb\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1.01,\"output_black\":3,\"output_white\":255},\"r\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.99,\"output_black\":0,\"output_white\":255},\"g\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.99,\"output_black\":0,\"output_white\":255},\"b\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.99,\"output_black\":0,\"output_white\":255},\"mask\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1,\"output_black\":0,\"output_white\":255}}", false, 0, 0.01, 0, 1.01, 1, 3, 255, null, null, null, null, null], "custom_all_levels": {"rgb": {"black_point": 0, "white_point": 1, "mid_point": 1.01, "output_black": 3, "output_white": 255}, "r": {"black_point": 0, "white_point": 1, "mid_point": 0.99, "output_black": 0, "output_white": 255}, "g": {"black_point": 0, "white_point": 1, "mid_point": 0.99, "output_black": 0, "output_white": 255}, "b": {"black_point": 0, "white_point": 1, "mid_point": 0.99, "output_black": 0, "output_white": 255}, "mask": {"black_point": 0, "white_point": 1, "mid_point": 1, "output_black": 0, "output_white": 255}}}, {"id": 1088, "type": "EsesImageEffectLevels", "pos": [5534.80859375, -947.5674438476562], "size": [210, 862.7626953125], "flags": {"collapsed": true}, "order": 143, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 2014}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "adjusted_image", "name": "adjusted_image", "type": "IMAGE", "links": [2005]}, {"label": "adjusted_mask", "name": "adjusted_mask", "type": "MASK", "links": null}, {"label": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"aux_id": "quasiblob/ComfyUI-EsesImageEffectLevels", "ver": "1db7439321f785e71d05d3c5eeb3d017f6edfea6", "Node name for S&R": "EsesImageEffectLevels", "widget_ue_connectable": {}}, "widgets_values": ["None", "RGB", "{\"rgb\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1.05,\"output_black\":5,\"output_white\":255},\"r\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.98,\"output_black\":0,\"output_white\":255},\"g\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.98,\"output_black\":0,\"output_white\":255},\"b\":{\"black_point\":0,\"white_point\":1,\"mid_point\":0.98,\"output_black\":0,\"output_white\":255},\"mask\":{\"black_point\":0,\"white_point\":1,\"mid_point\":1,\"output_black\":0,\"output_white\":255}}", false, 0, 0.05, 0, 1.05, 1, 5, 255, null, null, null, null, null], "custom_all_levels": {"rgb": {"black_point": 0, "white_point": 1, "mid_point": 1.05, "output_black": 5, "output_white": 255}, "r": {"black_point": 0, "white_point": 1, "mid_point": 0.98, "output_black": 0, "output_white": 255}, "g": {"black_point": 0, "white_point": 1, "mid_point": 0.98, "output_black": 0, "output_white": 255}, "b": {"black_point": 0, "white_point": 1, "mid_point": 0.98, "output_black": 0, "output_white": 255}, "mask": {"black_point": 0, "white_point": 1, "mid_point": 1, "output_black": 0, "output_white": 255}}}, {"id": 1097, "type": "DWPreprocessor", "pos": [1660.7322998046875, -4576.8935546875], "size": [294.72265625, 222], "flags": {}, "order": 98, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 2024}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2026]}, {"label": "姿态关键点", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT", "links": null}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "83463c2e4b04e729268e57f638b4212e0da4badc", "Node name for S&R": "DWPreprocessor", "widget_ue_connectable": {}}, "widgets_values": ["enable", "enable", "disable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384_bs5.torchscript.pt", "disable"]}, {"id": 1098, "type": "AIO_Preprocessor", "pos": [1668.719970703125, -4287.513671875], "size": [270, 82], "flags": {}, "order": 99, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 2025}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [2027]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "59b027e088c1c8facf7258f6e392d16d204b4d27", "Node name for S&R": "AIO_Preprocessor", "widget_ue_connectable": {}}, "widgets_values": ["DepthAnythingV2Preprocessor", 1024]}, {"id": 1009, "type": "WanVideoBlockSwap", "pos": [888.7740478515625, -3135.449462890625], "size": [315, 154], "flags": {}, "order": 67, "mode": 0, "inputs": [], "outputs": [{"label": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": [1858]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoBlockSwap", "widget_ue_connectable": {}}, "widgets_values": [40, false, false, true, 15], "color": "#223", "bgcolor": "#335"}, {"id": 1011, "type": "WanVideoModelLoader", "pos": [1420.3582763671875, -3335.2998046875], "size": [477.4410095214844, 274], "flags": {}, "order": 92, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS", "link": 1857}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 1858}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 1859}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS", "link": null}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH", "link": null}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL", "link": null}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL", "link": null}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [1990, 2031]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoModelLoader", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_VACE-Q4_K_S.gguf", "fp16_fast", "disabled", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 1017, "type": "WanVideoSampler", "pos": [4496.41552734375, -3722.869384765625], "size": [312.8956298828125, 878], "flags": {}, "order": 131, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 1871}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 2056}, {"label": "text_embeds", "name": "text_embeds", "shape": 7, "type": "WANVIDEOTEXTEMBEDS", "link": 1873}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": null}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 1992}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT", "link": null}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 1993}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS", "link": null}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 1874}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS", "link": null}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 1875}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS", "link": null}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE", "link": null}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS", "link": null}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS", "link": null}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS", "link": null}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS", "link": null}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 1881}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 2074}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "links": [1876]}], "properties": {"cnr_id": "舒适UI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoSampler", "widget_ue_connectable": {}}, "widgets_values": [4, 1.0000000000000002, 8.000000000000002, 718602933022343, "fixed", true, "unipc", 0, 1, false, "comfy", 0, -1]}, {"id": 1027, "type": "WanVideoSampler", "pos": [4357.8583984375, -1249.7822265625], "size": [312.8956298828125, 878], "flags": {}, "order": 136, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 1897}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 2062}, {"label": "text_embeds", "name": "text_embeds", "shape": 7, "type": "WANVIDEOTEXTEMBEDS", "link": 1969}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": null}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 2019}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT", "link": null}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 2020}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS", "link": null}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 1883}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS", "link": null}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 1884}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS", "link": null}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE", "link": null}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS", "link": null}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS", "link": null}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS", "link": null}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS", "link": null}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 1885}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 1886}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "links": [1900]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoSampler", "widget_ue_connectable": {}}, "widgets_values": [4, 1.0000000000000002, 8.000000000000002, 18, "fixed", true, "unipc", 0, 1, false, "comfy", 0, -1]}, {"id": 1030, "type": "WanVideoExperimentalArgs", "pos": [4047.064453125, -950.74365234375], "size": [327.5999755859375, 242], "flags": {"collapsed": true}, "order": 68, "mode": 0, "inputs": [], "outputs": [{"label": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [1884]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoExperimentalArgs", "widget_ue_connectable": {}}, "widgets_values": ["", true, false, 0, false, 1, 1.25, 20, false]}, {"id": 1092, "type": "WanVideoEasyCache", "pos": [4217.96630859375, -1433.84033203125], "size": [270, 130], "flags": {}, "order": 69, "mode": 0, "inputs": [], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [2020]}], "properties": {"cnr_id": "comfyui-wan<PERSON><PERSON><PERSON><PERSON>", "ver": "1.2.5", "Node name for S&R": "WanVideoEasyCache", "widget_ue_connectable": {}}, "widgets_values": [0.015, 1, -1, "offload_device"]}, {"id": 1061, "type": "PrimitiveStringMultiline", "pos": [379.28509521484375, -1555.697509765625], "size": [1427.0924072265625, 226.00758361816406], "flags": {}, "order": 70, "mode": 0, "inputs": [], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [1960, 1962]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "PrimitiveStringMultiline", "widget_ue_connectable": {}}, "widgets_values": ["(masterpiece, best quality, anime screencap, cel shading, clean lineart, vibrant colors, by Kyoto Animation, trending on Pixiv),一个戴着飞行员护目镜、扎着棕色双马尾的活泼女孩, 她穿着维多利亚风格的衬衫和皮质紧身胸衣, 搭配着带有大量齿轮和黄铜装饰的机械臂在走秀\n(masterpiece, best quality, anime screencap, cel shading, clean lineart, vibrant colors, by Kyoto Animation, trending on Pixiv),一个戴着飞行员护目镜、扎着棕色双马尾的活泼女孩, 她穿着维多利亚风格的衬衫和皮质紧身胸衣, 搭配着带有大量齿轮和黄铜装饰的机械臂在走秀\n(masterpiece, best quality, anime screencap, cel shading, clean lineart, vibrant colors, by Kyoto Animation, trending on Pixiv),一个戴着飞行员护目镜、扎着棕色双马尾的活泼女孩, 她穿着维多利亚风格的衬衫和皮质紧身胸衣, 搭配着带有大量齿轮和黄铜装饰的机械臂在走秀", [false, true]]}], "links": [[1625, 947, 0, 851, 0, "*"], [1626, 946, 0, 852, 0, "*"], [1627, 927, 0, 853, 0, "*"], [1628, 944, 0, 854, 0, "*"], [1658, 975, 0, 883, 0, "*"], [1675, 900, 0, 902, 2, "INT"], [1676, 901, 0, 902, 3, "INT"], [1698, 928, 0, 922, 0, "*"], [1743, 850, 0, 951, 0, "INT,FLOAT,IMAGE,LATENT"], [1744, 953, 1, 952, 0, "*"], [1745, 956, 0, 953, 0, "IMAGE"], [1752, 966, 2, 956, 0, "IMAGE"], [1753, 958, 0, 956, 2, "INT"], [1754, 959, 0, 956, 3, "INT"], [1757, 967, 0, 958, 0, "INT,FLOAT,IMAGE,LATENT"], [1758, 959, 0, 958, 1, "INT,FLOAT,IMAGE,LATENT"], [1761, 950, 0, 961, 0, "IMAGE"], [1762, 951, 0, 961, 1, "INT"], [1763, 960, 0, 961, 2, "INT"], [1764, 953, 0, 962, 0, "*"], [1769, 972, 0, 966, 0, "*"], [1770, 965, 0, 966, 1, "INT"], [1772, 949, 0, 966, 2, "*"], [1773, 966, 2, 967, 0, "IMAGE"], [1774, 966, 1, 968, 0, "*"], [1775, 966, 2, 969, 0, "IMAGE"], [1776, 961, 0, 969, 1, "IMAGE"], [1778, 966, 0, 971, 0, "FLOW_CONTROL"], [1779, 969, 0, 971, 1, "*"], [1802, 979, 0, 978, 0, "*"], [1809, 918, 0, 980, 4, "INT"], [1818, 986, 0, 987, 1, "UPSCALER_TRT_MODEL"], [1832, 971, 0, 991, 0, "*"], [1833, 971, 0, 993, 0, "*"], [1834, 998, 0, 906, 0, "INT"], [1844, 916, 0, 1000, 4, "INT"], [1857, 1008, 0, 1011, 0, "WANCOMPILEARGS"], [1858, 1009, 0, 1011, 1, "BLOCKSWAPARGS"], [1859, 1010, 0, 1011, 2, "WANVIDLORA"], [1860, 1014, 0, 1013, 0, "CLIP"], [1861, 1014, 0, 1015, 0, "CLIP"], [1867, 942, 0, 1018, 2, "IMAGE"], [1868, 1012, 0, 1019, 0, "*"], [1869, 1020, 0, 1018, 0, "WANVAE"], [1871, 1021, 0, 1017, 0, "WANVIDEOMODEL"], [1873, 1016, 0, 1017, 2, "WANVIDEOTEXTEMBEDS"], [1874, 1023, 0, 1017, 8, "SLGARGS"], [1875, 1024, 0, 1017, 10, "EXPERIMENTALARGS"], [1876, 1017, 0, 1025, 1, "LATENT"], [1877, 1026, 0, 1025, 0, "WANVAE"], [1878, 1025, 0, 923, 0, "*"], [1881, 916, 0, 1017, 17, "INT"], [1883, 1029, 0, 1027, 8, "SLGARGS"], [1884, 1030, 0, 1027, 10, "EXPERIMENTALARGS"], [1885, 875, 0, 1027, 17, "INT"], [1886, 884, 0, 1027, 18, "INT"], [1888, 1031, 0, 1028, 0, "WANVAE"], [1889, 868, 0, 1018, 5, "INT"], [1890, 829, 0, 1018, 6, "INT"], [1891, 918, 0, 1018, 7, "INT"], [1892, 887, 0, 1028, 5, "INT"], [1893, 888, 0, 1028, 6, "INT"], [1895, 943, 0, 1028, 2, "IMAGE"], [1897, 1032, 0, 1027, 0, "WANVIDEOMODEL"], [1900, 1027, 0, 1034, 1, "LATENT"], [1901, 1035, 0, 1034, 0, "WANVAE"], [1903, 1034, 0, 894, 0, "*"], [1918, 1039, 0, 1040, 0, "IMAGE"], [1920, 982, 0, 1042, 0, "INT,FLOAT,IMAGE,LATENT"], [1921, 983, 0, 1042, 1, "INT,FLOAT,IMAGE,LATENT"], [1922, 1042, 0, 965, 0, "INT,FLOAT,IMAGE,LATENT"], [1923, 972, 0, 1044, 0, "IMAGE"], [1924, 1044, 0, 965, 1, "INT,FLOAT,IMAGE,LATENT"], [1925, 959, 0, 965, 2, "INT,FLOAT,IMAGE,LATENT"], [1936, 951, 0, 960, 1, "INT,FLOAT,IMAGE,LATENT"], [1939, 1034, 0, 1050, 0, "IMAGE"], [1940, 1050, 0, 1051, 0, "*"], [1945, 949, 0, 1054, 0, "INT,FLOAT,IMAGE,LATENT"], [1946, 959, 0, 1054, 1, "INT,FLOAT,IMAGE,LATENT"], [1949, 949, 0, 1052, 0, "INT,FLOAT,IMAGE,LATENT"], [1950, 966, 1, 1052, 1, "INT,FLOAT,IMAGE,LATENT"], [1951, 1054, 0, 1052, 2, "INT,FLOAT,IMAGE,LATENT"], [1952, 1042, 0, 1055, 0, "INT,FLOAT,IMAGE,LATENT"], [1953, 1052, 0, 1055, 1, "INT,FLOAT,IMAGE,LATENT"], [1954, 949, 0, 1055, 2, "INT,FLOAT,IMAGE,LATENT"], [1955, 1055, 0, 1028, 7, "INT"], [1956, 1055, 0, 960, 0, "INT,FLOAT,IMAGE,LATENT"], [1957, 967, 0, 1056, 0, "*"], [1958, 1039, 0, 987, 0, "IMAGE"], [1960, 1061, 0, 1062, 0, "STRING"], [1961, 1060, 0, 1062, 1, "INT"], [1962, 1061, 0, 1064, 0, "STRING"], [1963, 1063, 0, 1064, 1, "INT"], [1964, 1064, 0, 1065, 1, "STRING"], [1965, 1014, 0, 1065, 0, "CLIP"], [1968, 1062, 0, 1015, 1, "STRING"], [1969, 1066, 0, 1027, 2, "WANVIDEOTEXTEMBEDS"], [1970, 966, 1, 1063, 0, "INT,FLOAT,IMAGE,LATENT"], [1971, 1044, 0, 1067, 0, "*"], [1976, 1036, 0, 1039, 0, "IMAGE"], [1987, 1069, 0, 1070, 0, "IMAGE"], [1988, 1069, 0, 902, 0, "IMAGE"], [1989, 902, 0, 879, 0, "IMAGE"], [1990, 1011, 0, 1077, 0, "WANVIDEOMODEL"], [1992, 1078, 0, 1017, 4, "FETAARGS"], [1993, 1079, 0, 1017, 6, "CACHEARGS"], [1995, 1025, 0, 1080, 0, "IMAGE"], [1996, 1080, 0, 945, 0, "IMAGE"], [1997, 991, 0, 1036, 0, "IMAGE"], [2000, 1084, 0, 1083, 0, "IMAGE"], [2001, 1084, 0, 1083, 1, "IMAGE"], [2002, 1084, 0, 1083, 2, "IMAGE"], [2003, 1084, 0, 1083, 3, "IMAGE"], [2004, 1089, 0, 1083, 4, "INT"], [2005, 1088, 0, 1085, 0, "IMAGE"], [2006, 1082, 0, 1085, 1, "IMAGE"], [2007, 1081, 0, 1085, 2, "IMAGE"], [2008, 1086, 0, 1085, 3, "IMAGE"], [2009, 1089, 0, 1085, 4, "INT"], [2010, 1090, 0, 1087, 0, "*"], [2011, 1083, 0, 1087, 1, "*"], [2012, 1085, 0, 1087, 2, "*"], [2013, 1087, 0, 950, 0, "IMAGE"], [2014, 1034, 0, 1088, 0, "IMAGE"], [2015, 1034, 0, 1082, 0, "IMAGE"], [2016, 1034, 0, 1081, 0, "IMAGE"], [2017, 1034, 0, 1086, 0, "IMAGE"], [2018, 1034, 0, 1084, 0, "IMAGE"], [2019, 1091, 0, 1027, 4, "FETAARGS"], [2020, 1092, 0, 1027, 6, "CACHEARGS"], [2021, 966, 1, 1089, 0, "INT,FLOAT,IMAGE,LATENT"], [2022, 966, 1, 1090, 0, "INT,FLOAT,IMAGE,LATENT"], [2024, 1096, 0, 1097, 0, "IMAGE"], [2025, 1096, 0, 1098, 0, "IMAGE"], [2026, 1097, 0, 1099, 0, "IMAGE"], [2027, 1098, 0, 1099, 1, "IMAGE"], [2028, 1099, 0, 1100, 0, "IMAGE"], [2029, 1102, 0, 1100, 2, "INT"], [2030, 1101, 0, 1100, 3, "INT"], [2031, 1011, 0, 1022, 0, "WANVIDEOMODEL"], [2033, 1096, 2, 1104, 0, "*"], [2035, 1100, 0, 1018, 1, "IMAGE"], [2036, 945, 0, 1105, 0, "IMAGE"], [2037, 994, 0, 1105, 4, "FLOAT"], [2038, 1104, 0, 1105, 1, "AUDIO"], [2041, 1055, 0, 953, 4, "INT"], [2043, 958, 0, 1106, 2, "INT"], [2044, 1100, 0, 1106, 0, "IMAGE"], [2045, 1106, 0, 953, 2, "IMAGE"], [2047, 1015, 0, 1108, 0, "*"], [2048, 1108, 0, 1016, 0, "CONDITIONING"], [2049, 1013, 0, 1109, 0, "*"], [2050, 1109, 0, 1016, 1, "CONDITIONING"], [2051, 1013, 0, 1110, 0, "*"], [2052, 1110, 0, 1066, 1, "CONDITIONING"], [2053, 1065, 0, 1111, 0, "*"], [2054, 1111, 0, 1066, 0, "CONDITIONING"], [2055, 1018, 0, 1112, 0, "*"], [2056, 1112, 0, 1017, 1, "WANVIDIMAGE_EMBEDS"], [2057, 848, 0, 1113, 0, "*"], [2058, 1113, 0, 1028, 3, "MASK"], [2059, 895, 0, 1114, 0, "*"], [2060, 1114, 0, 1028, 1, "IMAGE"], [2061, 1028, 0, 1115, 0, "*"], [2062, 1115, 0, 1027, 1, "WANVIDIMAGE_EMBEDS"], [2063, 1099, 0, 1116, 0, "*"], [2064, 1055, 0, 1106, 3, "INT"], [2065, 1095, 0, 1096, 2, "FLOAT"], [2068, 1104, 0, 1119, 1, "AUDIO"], [2069, 996, 0, 1119, 4, "FLOAT"], [2070, 969, 0, 1119, 0, "IMAGE"], [2071, 992, 0, 1120, 4, "FLOAT"], [2072, 991, 0, 1120, 0, "IMAGE"], [2073, 1104, 0, 1120, 1, "AUDIO"], [2074, 915, 0, 1017, 18, "INT"]], "groups": [{"id": 34, "title": "模型", "bounding": [-176.55606079101562, -3735.791015625, 2728.89697265625, 2441.225830078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 35, "title": "初次采样", "bounding": [2870.806884765625, -3959.122314453125, 3472.916015625, 1146.4495849609375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 36, "title": "循环采样", "bounding": [2848.7255859375, -2522.792236328125, 4971.52490234375, 2148.423828125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4177248169415962, "offset": [966.165942071735, 6016.166985480531]}, "frontendVersion": "1.23.4", "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "comfy-core": "0.3.26", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "ue_links": [], "links_added_by_ue": []}, "version": 0.4}